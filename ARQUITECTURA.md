# **Arquitectura Técnica - Aplicación de Atención Ciudadana AI-First**

## **1. Resumen Ejecutivo de la Arquitectura**

Esta arquitectura define una aplicación de atención ciudadana moderna, escalable y centrada en IA, construida sobre **Next.js 15** con **App Router** como framework fullstack y **Supabase** como plataforma backend. La solución implementa un enfoque "AI-first" con dos fases de desarrollo: consultas inteligentes (Fase 1) y automatización de trámites (Fase 2).

### **Principios Arquitectónicos**
- **AI-First**: IA integrada desde el núcleo de la aplicación
- **Escalabilidad**: Arquitectura serverless y distribuida
- **Seguridad**: Row Level Security (RLS) y autenticación robusta
- **Interoperabilidad**: APIs estándar y formatos de datos abiertos
- **Accesibilidad**: Cumplimiento WCAG 2.2 AA
- **Rendimiento**: SSR, caching inteligente y optimización de recursos

## **2. Stack Tecnológico Detallado**

### **2.1 Frontend y Framework Principal**
- **Next.js 15** con App Router
  - **Justificación**: Framework React moderno con SSR/SSG nativo, API routes integradas, optimizaciones automáticas y excelente DX
  - **Características**: Server Components, Streaming, Parallel Routes, Intercepting Routes
  - **Rendering**: Híbrido (SSR para SEO, CSR para interactividad)

### **2.2 Backend y Base de Datos**
- **Supabase** (PostgreSQL + APIs + Auth + Storage + Edge Functions)
  - **Justificación**: BaaS completo con PostgreSQL, autenticación JWT, RLS nativo, APIs REST/GraphQL automáticas
  - **Componentes**:
    - PostgreSQL 15+ con extensiones (pgvector para embeddings)
    - GoTrue (autenticación JWT)
    - PostgREST (API REST automática)
    - Realtime (WebSockets)
    - Storage (archivos)
    - Edge Functions (Deno runtime)

### **2.3 Inteligencia Artificial**
- **OpenAI GPT-4** para chatbot conversacional
- **OpenAI Embeddings** para búsqueda semántica
- **Langchain.js** para orquestación de IA
- **Pinecone/pgvector** para almacenamiento vectorial

### **2.4 Tecnologías Complementarias**
- **TypeScript** para type safety
- **Tailwind CSS** para styling
- **Shadcn/ui** para componentes UI
- **React Hook Form + Zod** para formularios y validación
- **Tanstack Query** para state management y caching
- **NextAuth.js** integrado con Supabase Auth

## **3. Arquitectura de Componentes y Módulos**

### **3.1 Estructura de Alto Nivel**

```mermaid
graph TB
    subgraph "Frontend (Next.js 15)"
        A[Portal Ciudadano] --> B[Chatbot IA]
        A --> C[Búsqueda Semántica]
        A --> D[Carpeta Ciudadana]
        A --> E[Seguimiento Trámites]
        
        F[Panel Admin] --> G[Gestión Contenido]
        F --> H[Gestión Usuarios]
        F --> I[Analytics & Monitoreo]
    end
    
    subgraph "Backend (Supabase)"
        J[PostgreSQL + RLS] --> K[API REST/GraphQL]
        J --> L[Auth (GoTrue)]
        J --> M[Storage]
        J --> N[Realtime]
        
        O[Edge Functions] --> P[IA Processing]
        O --> Q[Webhooks]
        O --> R[Automatización]
    end
    
    subgraph "Servicios IA"
        S[OpenAI GPT-4]
        T[OpenAI Embeddings]
        U[Vector Database]
    end
    
    A --> K
    F --> K
    B --> P
    C --> U
    P --> S
    P --> T
```

### **3.2 Módulos Principales**

#### **3.2.1 Módulo de Atención Ciudadana (Frontend)**
```typescript
// Estructura de carpetas
src/
├── app/
│   ├── (ciudadano)/
│   │   ├── page.tsx                 // Portal principal
│   │   ├── chat/
│   │   │   └── page.tsx            // Chatbot IA
│   │   ├── buscar/
│   │   │   └── page.tsx            // Búsqueda semántica
│   │   ├── carpeta/
│   │   │   └── page.tsx            // Carpeta ciudadana
│   │   └── tramites/
│   │       └── [id]/page.tsx       // Seguimiento trámites
│   └── (admin)/
│       ├── dashboard/
│       ├── contenido/
│       ├── usuarios/
│       └── analytics/
```

#### **3.2.2 Módulo de Backend (Supabase)**
- **Edge Functions**: Procesamiento IA, webhooks, automatización
- **Database**: Esquemas relacionales con RLS
- **Auth**: Gestión de usuarios y roles
- **Storage**: Documentos y archivos adjuntos

## **4. Diseño de Base de Datos**

### **4.1 Esquema Principal**

```sql
-- Esquema de dependencias gubernamentales
CREATE TABLE dependencias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    contacto JSONB,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subdependencias
CREATE TABLE subdependencias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dependencia_id UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    contacto JSONB,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categorías (Trámites, OPA, Servicios)
CREATE TABLE categorias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subdependencia_id UUID REFERENCES subdependencias(id) ON DELETE CASCADE,
    tipo categoria_tipo NOT NULL, -- ENUM: 'tramite', 'opa', 'servicio'
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    palabras_clave TEXT[],
    metadatos JSONB,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Embeddings para búsqueda semántica
CREATE TABLE categoria_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    categoria_id UUID REFERENCES categorias(id) ON DELETE CASCADE,
    embedding VECTOR(1536), -- OpenAI embeddings dimension
    contenido_indexado TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usuarios ciudadanos
CREATE TABLE ciudadanos (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    nombre VARCHAR(255),
    apellido VARCHAR(255),
    identificacion VARCHAR(50) UNIQUE,
    email VARCHAR(255),
    telefono VARCHAR(20),
    metadatos JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Consultas del chatbot
CREATE TABLE consultas_ia (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ciudadano_id UUID REFERENCES ciudadanos(id),
    sesion_id VARCHAR(255),
    pregunta TEXT NOT NULL,
    respuesta TEXT,
    contexto JSONB,
    feedback INTEGER CHECK (feedback >= 1 AND feedback <= 5),
    resuelto BOOLEAN DEFAULT false,
    escalado BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Seguimiento de trámites
CREATE TABLE seguimiento_tramites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ciudadano_id UUID REFERENCES ciudadanos(id) ON DELETE CASCADE,
    categoria_id UUID REFERENCES categorias(id) ON DELETE CASCADE,
    numero_tramite VARCHAR(50) UNIQUE NOT NULL,
    estado estado_tramite DEFAULT 'iniciado',
    etapa_actual VARCHAR(100),
    datos_tramite JSONB,
    documentos JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **4.2 Políticas de Row Level Security (RLS)**

```sql
-- Habilitar RLS en todas las tablas
ALTER TABLE dependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE subdependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE categorias ENABLE ROW LEVEL SECURITY;
ALTER TABLE ciudadanos ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultas_ia ENABLE ROW LEVEL SECURITY;
ALTER TABLE seguimiento_tramites ENABLE ROW LEVEL SECURITY;

-- Políticas para ciudadanos
CREATE POLICY "Ciudadanos pueden ver información pública"
ON categorias FOR SELECT
TO authenticated
USING (activo = true);

CREATE POLICY "Ciudadanos pueden acceder a su carpeta"
ON ciudadanos FOR ALL
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Ciudadanos pueden ver sus consultas"
ON consultas_ia FOR ALL
TO authenticated
USING (ciudadano_id = auth.uid());

CREATE POLICY "Ciudadanos pueden ver sus trámites"
ON seguimiento_tramites FOR ALL
TO authenticated
USING (ciudadano_id = auth.uid());

-- Políticas para administradores
CREATE POLICY "Admins pueden gestionar contenido"
ON categorias FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM user_roles 
        WHERE user_id = auth.uid() 
        AND role IN ('admin', 'super_admin')
    )
);
```

## **5. Flujo de Datos y APIs**

### **5.1 Arquitectura de APIs**

```mermaid
sequenceDiagram
    participant C as Cliente
    participant N as Next.js
    participant S as Supabase
    participant AI as OpenAI
    participant V as Vector DB

    Note over C,V: Flujo de Consulta IA
    C->>N: Pregunta ciudadano
    N->>S: Autenticación JWT
    S-->>N: Usuario validado
    N->>AI: Procesar pregunta (GPT-4)
    N->>V: Búsqueda semántica
    V-->>N: Contexto relevante
    AI-->>N: Respuesta generada
    N->>S: Guardar consulta
    N-->>C: Respuesta + UI
```

### **5.2 Edge Functions para IA**

```typescript
// supabase/functions/chat-ia/index.ts
import { createClient } from 'npm:@supabase/supabase-js@2'
import { OpenAI } from 'npm:openai@4'

Deno.serve(async (req: Request) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL')!,
    Deno.env.get('SUPABASE_ANON_KEY')!,
    {
      global: {
        headers: { Authorization: req.headers.get('Authorization')! },
      },
    }
  )

  const { pregunta, contexto } = await req.json()
  
  // Búsqueda semántica
  const embedding = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: pregunta,
  })

  const { data: contextoDocs } = await supabase.rpc('buscar_semantica', {
    query_embedding: embedding.data[0].embedding,
    match_threshold: 0.8,
    match_count: 5
  })

  // Generar respuesta con GPT-4
  const respuesta = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [
      {
        role: "system",
        content: `Eres un asistente de atención ciudadana. Contexto: ${JSON.stringify(contextoDocs)}`
      },
      {
        role: "user", 
        content: pregunta
      }
    ]
  })

  // Guardar consulta
  await supabase.from('consultas_ia').insert({
    pregunta,
    respuesta: respuesta.choices[0].message.content,
    contexto: contextoDocs
  })

  return new Response(JSON.stringify({
    respuesta: respuesta.choices[0].message.content,
    fuentes: contextoDocs
  }), {
    headers: { 'Content-Type': 'application/json' }
  })
})
```

## **6. Seguridad y Autenticación**

### **6.1 Estrategia de Autenticación**

```typescript
// Configuración NextAuth.js + Supabase
// app/api/auth/[...nextauth]/route.ts
import NextAuth from 'next-auth'
import { SupabaseAdapter } from '@auth/supabase-adapter'

export const authOptions = {
  adapter: SupabaseAdapter({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  }),
  providers: [
    // Autenticación con cédula/email + OTP
    EmailProvider({
      server: process.env.EMAIL_SERVER,
      from: process.env.EMAIL_FROM,
    }),
    // Integración con sistemas gubernamentales
    CredentialsProvider({
      name: 'Cédula',
      credentials: {
        cedula: { label: 'Cédula', type: 'text' },
        otp: { label: 'Código OTP', type: 'text' }
      },
      async authorize(credentials) {
        // Validación con sistemas externos
        return await validateCiudadano(credentials)
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.dependencia_id = user.dependencia_id
      }
      return token
    },
    async session({ session, token }) {
      session.user.role = token.role
      session.user.dependencia_id = token.dependencia_id
      return session
    }
  }
}
```

### **6.2 Roles y Permisos**

```sql
-- Tabla de roles de usuario
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role user_role_type NOT NULL, -- 'ciudadano', 'admin_dependencia', 'super_admin'
    dependencia_id UUID REFERENCES dependencias(id),
    permisos JSONB DEFAULT '{}',
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Función para verificar permisos
CREATE OR REPLACE FUNCTION auth.has_permission(
    required_permission TEXT,
    resource_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permisos INTO user_role, user_permissions
    FROM user_roles
    WHERE user_id = auth.uid() AND activo = true;

    -- Super admin tiene todos los permisos
    IF user_role = 'super_admin' THEN
        RETURN TRUE;
    END IF;

    -- Verificar permiso específico
    RETURN (user_permissions ? required_permission);
END;
$$;
```

### **6.3 Seguridad de Datos**

- **Encriptación**: TLS 1.3 en tránsito, AES-256 en reposo
- **Auditoría**: Logs completos de acceso y modificaciones
- **Backup**: Respaldos automáticos cada 6 horas con retención de 30 días
- **Compliance**: GDPR, LOPD, estándares gubernamentales colombianos

## **7. Estructura de Carpetas del Proyecto**

```
chia-next/
├── README.md
├── ARQUITECTURA.md
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── .env.local
├── .env.example
│
├── src/
│   ├── app/                          # App Router (Next.js 15)
│   │   ├── globals.css
│   │   ├── layout.tsx               # Layout raíz
│   │   ├── page.tsx                 # Página principal
│   │   ├── loading.tsx              # Loading UI
│   │   ├── error.tsx                # Error UI
│   │   ├── not-found.tsx            # 404 UI
│   │   │
│   │   ├── (auth)/                  # Grupo de rutas de autenticación
│   │   │   ├── login/
│   │   │   ├── register/
│   │   │   └── verify/
│   │   │
│   │   ├── (ciudadano)/             # Portal ciudadano
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx             # Dashboard ciudadano
│   │   │   ├── chat/                # Chatbot IA
│   │   │   │   ├── page.tsx
│   │   │   │   └── components/
│   │   │   ├── buscar/              # Búsqueda semántica
│   │   │   │   ├── page.tsx
│   │   │   │   └── components/
│   │   │   ├── carpeta/             # Carpeta ciudadana
│   │   │   │   ├── page.tsx
│   │   │   │   ├── perfil/
│   │   │   │   └── documentos/
│   │   │   └── tramites/            # Seguimiento trámites
│   │   │       ├── page.tsx
│   │   │       ├── [id]/
│   │   │       └── components/
│   │   │
│   │   ├── (admin)/                 # Panel administrativo
│   │   │   ├── layout.tsx
│   │   │   ├── dashboard/
│   │   │   │   └── page.tsx
│   │   │   ├── contenido/           # Gestión de contenido
│   │   │   │   ├── dependencias/
│   │   │   │   ├── categorias/
│   │   │   │   └── embeddings/
│   │   │   ├── usuarios/            # Gestión de usuarios
│   │   │   │   ├── ciudadanos/
│   │   │   │   └── administradores/
│   │   │   └── analytics/           # Métricas y reportes
│   │   │       ├── consultas/
│   │   │       ├── tramites/
│   │   │       └── rendimiento/
│   │   │
│   │   └── api/                     # API Routes
│   │       ├── auth/
│   │       ├── chat/
│   │       ├── search/
│   │       ├── tramites/
│   │       └── webhooks/
│   │
│   ├── components/                   # Componentes reutilizables
│   │   ├── ui/                      # Componentes base (shadcn/ui)
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   └── ...
│   │   ├── layout/                  # Componentes de layout
│   │   │   ├── header.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── footer.tsx
│   │   │   └── navigation.tsx
│   │   ├── chat/                    # Componentes del chatbot
│   │   │   ├── chat-interface.tsx
│   │   │   ├── message-bubble.tsx
│   │   │   └── typing-indicator.tsx
│   │   ├── forms/                   # Formularios
│   │   │   ├── contact-form.tsx
│   │   │   ├── search-form.tsx
│   │   │   └── tramite-form.tsx
│   │   └── common/                  # Componentes comunes
│   │       ├── loading-spinner.tsx
│   │       ├── error-boundary.tsx
│   │       └── accessibility.tsx
│   │
│   ├── lib/                         # Utilidades y configuraciones
│   │   ├── supabase/               # Cliente Supabase
│   │   │   ├── client.ts           # Cliente del navegador
│   │   │   ├── server.ts           # Cliente del servidor
│   │   │   └── middleware.ts       # Middleware de auth
│   │   ├── ai/                     # Servicios de IA
│   │   │   ├── openai.ts
│   │   │   ├── embeddings.ts
│   │   │   └── langchain.ts
│   │   ├── auth/                   # Configuración de autenticación
│   │   │   ├── config.ts
│   │   │   └── providers.ts
│   │   ├── utils/                  # Utilidades generales
│   │   │   ├── cn.ts               # Class name utility
│   │   │   ├── validators.ts       # Esquemas Zod
│   │   │   ├── formatters.ts       # Formateo de datos
│   │   │   └── constants.ts        # Constantes
│   │   └── hooks/                  # Custom hooks
│   │       ├── use-auth.ts
│   │       ├── use-chat.ts
│   │       ├── use-search.ts
│   │       └── use-tramites.ts
│   │
│   ├── types/                      # Definiciones de tipos TypeScript
│   │   ├── database.ts             # Tipos de la base de datos
│   │   ├── auth.ts                 # Tipos de autenticación
│   │   ├── chat.ts                 # Tipos del chat
│   │   └── api.ts                  # Tipos de API
│   │
│   └── styles/                     # Estilos globales
│       ├── globals.css
│       └── components.css
│
├── supabase/                       # Configuración Supabase
│   ├── config.toml
│   ├── seed.sql
│   ├── migrations/                 # Migraciones de BD
│   │   ├── 20240101000000_initial_schema.sql
│   │   ├── 20240101000001_rls_policies.sql
│   │   ├── 20240101000002_embeddings.sql
│   │   └── ...
│   └── functions/                  # Edge Functions
│       ├── chat-ia/
│       │   ├── index.ts
│       │   └── deno.json
│       ├── busqueda-semantica/
│       │   ├── index.ts
│       │   └── deno.json
│       ├── generar-embeddings/
│       │   ├── index.ts
│       │   └── deno.json
│       └── webhooks/
│           ├── tramites/
│           └── notificaciones/
│
├── docs/                           # Documentación
│   ├── api/                        # Documentación de API
│   ├── deployment/                 # Guías de despliegue
│   ├── development/                # Guías de desarrollo
│   └── user-guides/                # Guías de usuario
│
├── tests/                          # Pruebas
│   ├── __mocks__/
│   ├── unit/
│   ├── integration/
│   ├── e2e/
│   └── setup.ts
│
├── public/                         # Archivos estáticos
│   ├── icons/
│   ├── images/
│   ├── logos/
│   └── manifest.json
│
└── scripts/                        # Scripts de utilidad
    ├── setup-db.ts
    ├── generate-embeddings.ts
    ├── migrate.ts
    └── deploy.ts
```

## **8. Patrones de Diseño a Implementar**

### **8.1 Patrones de Frontend**

#### **8.1.1 Compound Components Pattern**
```typescript
// components/chat/chat-interface.tsx
interface ChatContextType {
  messages: Message[]
  sendMessage: (content: string) => void
  isLoading: boolean
}

const ChatContext = createContext<ChatContextType | null>(null)

export const Chat = ({ children }: { children: React.ReactNode }) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const sendMessage = async (content: string) => {
    setIsLoading(true)
    // Lógica de envío
    setIsLoading(false)
  }

  return (
    <ChatContext.Provider value={{ messages, sendMessage, isLoading }}>
      <div className="chat-container">{children}</div>
    </ChatContext.Provider>
  )
}

Chat.Messages = ({ children }: { children: React.ReactNode }) => (
  <div className="messages-container">{children}</div>
)

Chat.Input = () => {
  const context = useContext(ChatContext)
  // Implementación del input
}
```

#### **8.1.2 Custom Hooks Pattern**
```typescript
// lib/hooks/use-chat.ts
export const useChat = (sessionId?: string) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const sendMessage = useMutation({
    mutationFn: async (content: string) => {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content, sessionId })
      })
      return response.json()
    },
    onSuccess: (data) => {
      setMessages(prev => [...prev, data.message])
    }
  })

  return {
    messages,
    sendMessage: sendMessage.mutate,
    isLoading: sendMessage.isPending
  }
}
```

### **8.2 Patrones de Backend**

#### **8.2.1 Repository Pattern**
```typescript
// lib/repositories/tramite-repository.ts
export interface TramiteRepository {
  findById(id: string): Promise<Tramite | null>
  findByUserId(userId: string): Promise<Tramite[]>
  create(data: CreateTramiteData): Promise<Tramite>
  update(id: string, data: UpdateTramiteData): Promise<Tramite>
}

export class SupabaseTramiteRepository implements TramiteRepository {
  constructor(private supabase: SupabaseClient) {}

  async findById(id: string): Promise<Tramite | null> {
    const { data, error } = await this.supabase
      .from('seguimiento_tramites')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw new Error(error.message)
    return data
  }

  async findByUserId(userId: string): Promise<Tramite[]> {
    const { data, error } = await this.supabase
      .from('seguimiento_tramites')
      .select('*')
      .eq('ciudadano_id', userId)

    if (error) throw new Error(error.message)
    return data || []
  }
}
```

#### **8.2.2 Service Layer Pattern**
```typescript
// lib/services/ai-service.ts
export class AIService {
  constructor(
    private openai: OpenAI,
    private vectorStore: VectorStore,
    private tramiteRepo: TramiteRepository
  ) {}

  async processQuery(query: string, userId: string): Promise<AIResponse> {
    // 1. Generar embedding de la consulta
    const embedding = await this.generateEmbedding(query)

    // 2. Búsqueda semántica
    const context = await this.vectorStore.similaritySearch(embedding, 5)

    // 3. Obtener historial del usuario
    const userHistory = await this.tramiteRepo.findByUserId(userId)

    // 4. Generar respuesta con GPT-4
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: this.buildSystemPrompt(context, userHistory)
        },
        {
          role: "user",
          content: query
        }
      ]
    })

    return {
      answer: response.choices[0].message.content,
      sources: context,
      confidence: this.calculateConfidence(context)
    }
  }
}
```

### **8.3 Patrones de Seguridad**

#### **8.3.1 Middleware Pattern para Autenticación**
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Rutas protegidas
  if (req.nextUrl.pathname.startsWith('/admin')) {
    if (!session) {
      return NextResponse.redirect(new URL('/login', req.url))
    }

    // Verificar rol de administrador
    const { data: userRole } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single()

    if (!userRole || !['admin', 'super_admin'].includes(userRole.role)) {
      return NextResponse.redirect(new URL('/unauthorized', req.url))
    }
  }

  return res
}

export const config = {
  matcher: ['/admin/:path*', '/carpeta/:path*', '/tramites/:path*']
}
```

## **9. Consideraciones de Rendimiento y Escalabilidad**

### **9.1 Estrategias de Caching**

#### **9.1.1 Caching de Consultas IA**
```typescript
// lib/cache/ai-cache.ts
import { Redis } from 'ioredis'

export class AICacheService {
  private redis: Redis

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
  }

  async getCachedResponse(query: string): Promise<AIResponse | null> {
    const key = `ai:${this.hashQuery(query)}`
    const cached = await this.redis.get(key)
    return cached ? JSON.parse(cached) : null
  }

  async setCachedResponse(query: string, response: AIResponse): Promise<void> {
    const key = `ai:${this.hashQuery(query)}`
    await this.redis.setex(key, 3600, JSON.stringify(response)) // 1 hora TTL
  }

  private hashQuery(query: string): string {
    return crypto.createHash('sha256').update(query.toLowerCase()).digest('hex')
  }
}
```

#### **9.1.2 Caching de Embeddings**
```sql
-- Índice para búsquedas vectoriales eficientes
CREATE INDEX ON categoria_embeddings
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Función optimizada para búsqueda semántica
CREATE OR REPLACE FUNCTION buscar_semantica(
  query_embedding vector(1536),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  categoria_id uuid,
  nombre text,
  descripcion text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id,
    c.nombre,
    c.descripcion,
    1 - (ce.embedding <=> query_embedding) as similarity
  FROM categoria_embeddings ce
  JOIN categorias c ON ce.categoria_id = c.id
  WHERE 1 - (ce.embedding <=> query_embedding) > match_threshold
    AND c.activo = true
  ORDER BY ce.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
```

### **9.2 Optimizaciones de Base de Datos**

```sql
-- Índices para consultas frecuentes
CREATE INDEX idx_consultas_ia_ciudadano_fecha
ON consultas_ia(ciudadano_id, created_at DESC);

CREATE INDEX idx_tramites_estado_fecha
ON seguimiento_tramites(estado, created_at DESC);

CREATE INDEX idx_categorias_tipo_activo
ON categorias(tipo, activo) WHERE activo = true;

-- Particionado por fecha para tablas de logs
CREATE TABLE consultas_ia_2024 PARTITION OF consultas_ia
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Configuración de conexiones
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

### **9.3 Monitoreo y Métricas**

```typescript
// lib/monitoring/metrics.ts
export class MetricsService {
  private static instance: MetricsService

  async trackChatInteraction(userId: string, responseTime: number, satisfied: boolean) {
    await this.sendMetric('chat.interaction', {
      userId,
      responseTime,
      satisfied,
      timestamp: new Date().toISOString()
    })
  }

  async trackSearchQuery(query: string, resultsCount: number, clickThrough: boolean) {
    await this.sendMetric('search.query', {
      queryLength: query.length,
      resultsCount,
      clickThrough,
      timestamp: new Date().toISOString()
    })
  }

  async trackTramiteProgress(tramiteId: string, fromState: string, toState: string) {
    await this.sendMetric('tramite.progress', {
      tramiteId,
      fromState,
      toState,
      timestamp: new Date().toISOString()
    })
  }

  private async sendMetric(event: string, data: any) {
    // Integración con servicio de métricas (DataDog, New Relic, etc.)
    if (process.env.NODE_ENV === 'production') {
      await fetch(process.env.METRICS_ENDPOINT!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ event, data })
      })
    }
  }
}
```

## **10. Plan de Despliegue y DevOps**

### **10.1 Arquitectura de Despliegue**

```mermaid
graph TB
    subgraph "Producción"
        A[Vercel Edge Network] --> B[Next.js App]
        B --> C[Supabase Cloud]
        C --> D[PostgreSQL]
        C --> E[Edge Functions]

        F[CDN] --> G[Assets Estáticos]
        H[Redis Cache] --> B
        I[Monitoring] --> B
        I --> C
    end

    subgraph "Staging"
        J[Vercel Preview] --> K[Supabase Staging]
    end

    subgraph "Development"
        L[Local Dev] --> M[Supabase Local]
    end
```

### **10.2 Pipeline CI/CD**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test:ci

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Type check
        run: npm run type-check

      - name: Lint
        run: npm run lint

  deploy-staging:
    needs: test
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to Vercel Preview
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

  deploy-production:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to Vercel Production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

      - name: Run Supabase migrations
        run: |
          npx supabase db push --db-url ${{ secrets.SUPABASE_DB_URL }}

      - name: Deploy Edge Functions
        run: |
          npx supabase functions deploy --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
```

### **10.3 Configuración de Entornos**

```bash
# .env.example
# Next.js
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_JWT_SECRET=your_jwt_secret

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Autenticación
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Base de datos
DATABASE_URL=your_database_url

# Redis (opcional para caching)
REDIS_URL=your_redis_url

# Monitoreo
SENTRY_DSN=your_sentry_dsn
ANALYTICS_ID=your_analytics_id

# Email
EMAIL_SERVER=smtp://username:<EMAIL>:587
EMAIL_FROM=<EMAIL>
```

### **10.4 Monitoreo y Alertas**

```typescript
// lib/monitoring/alerts.ts
export class AlertService {
  async setupHealthChecks() {
    // Health check para la aplicación
    setInterval(async () => {
      try {
        const response = await fetch('/api/health')
        if (!response.ok) {
          await this.sendAlert('App health check failed', 'critical')
        }
      } catch (error) {
        await this.sendAlert(`App unreachable: ${error}`, 'critical')
      }
    }, 60000) // Cada minuto

    // Health check para Supabase
    setInterval(async () => {
      try {
        const { data, error } = await supabase.from('dependencias').select('count').limit(1)
        if (error) {
          await this.sendAlert(`Database error: ${error.message}`, 'high')
        }
      } catch (error) {
        await this.sendAlert(`Database unreachable: ${error}`, 'critical')
      }
    }, 300000) // Cada 5 minutos
  }

  private async sendAlert(message: string, severity: 'low' | 'medium' | 'high' | 'critical') {
    // Integración con sistema de alertas (Slack, PagerDuty, etc.)
    if (process.env.NODE_ENV === 'production') {
      await fetch(process.env.WEBHOOK_ALERTS!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 ${severity.toUpperCase()}: ${message}`,
          timestamp: new Date().toISOString()
        })
      })
    }
  }
}
```

---

## **Conclusión**

Esta arquitectura proporciona una base sólida, escalable y moderna para la aplicación de atención ciudadana AI-first. La combinación de Next.js 15 y Supabase ofrece:

- **Desarrollo ágil** con herramientas modernas y DX optimizado
- **Escalabilidad automática** mediante arquitectura serverless
- **Seguridad robusta** con RLS, autenticación JWT y auditoría completa
- **IA integrada** desde el núcleo con capacidades conversacionales y de búsqueda semántica
- **Mantenibilidad** a través de patrones de diseño establecidos y código bien estructurado

La implementación seguirá un enfoque iterativo, comenzando con la Fase 1 (consultas IA) y evolucionando hacia la Fase 2 (automatización de trámites), manteniendo siempre los estándares de calidad, seguridad y accesibilidad requeridos para aplicaciones gubernamentales.
