# **ARQUITECTURA TÉCNICA - APLICACIÓN DE ATENCIÓN CIUDADANA AI-FIRST**

## **1. RESUMEN EJECUTIVO DE ARQUITECTURA**

### **1.1 Visión Arquitectónica**
Esta arquitectura define una aplicación de atención ciudadana AI-first construida sobre **Next.js 15** y **Supabase**, diseñada para transformar la interacción gubernamental mediante inteligencia artificial. La solución implementa un enfoque de dos fases: consultas inteligentes (Fase 1) y automatización de trámites (Fase 2).

### **1.2 Principios Arquitectónicos**
- **AI-First Design**: La IA es el núcleo de la experiencia, no una característica adicional
- **Seguridad por Diseño**: Row Level Security (RLS) y autenticación robusta desde el primer día
- **Escalabilidad Gubernamental**: Arquitectura preparada para millones de ciudadanos
- **Interoperabilidad**: APIs abiertas y estándares para integración con sistemas existentes
- **Accesibilidad Universal**: Cumplimiento WCAG 2.2 AA como requisito fundamental

### **1.3 Decisiones Arquitectónicas Clave**
- **Frontend/Fullstack**: Next.js 15 con App Router para SSR/SSG optimizado
- **Backend/Base de Datos**: Supabase (PostgreSQL + GoTrue + PostgREST + Edge Functions)
- **IA/ML**: OpenAI GPT-4 + Embeddings con pgvector para búsqueda semántica
- **Autenticación**: Supabase Auth + NextAuth.js para máxima flexibilidad
- **Despliegue**: Vercel + Supabase Cloud con CI/CD automatizado

---

## **2. STACK TECNOLÓGICO DETALLADO**

### **2.1 Frontend - Next.js 15**
```typescript
// Configuración Next.js optimizada para gobierno
// next.config.js
const nextConfig = {
  experimental: {
    serverActions: true,
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  images: {
    domains: ['supabase.co'],
    formats: ['image/webp', 'image/avif']
  },
  i18n: {
    locales: ['es', 'en'],
    defaultLocale: 'es'
  }
}
```

**Justificación Next.js 15**:
- **Server Components**: Renderizado del lado del servidor para mejor SEO y performance
- **App Router**: Routing basado en archivos con layouts anidados
- **Server Actions**: Mutaciones de datos sin APIs explícitas
- **Streaming**: Carga progresiva para mejor UX
- **Built-in Optimizations**: Imágenes, fuentes y scripts optimizados automáticamente

### **2.2 Backend - Supabase**
```typescript
// Configuración Supabase
const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
}
```

**Justificación Supabase**:
- **PostgreSQL**: Base de datos relacional robusta con extensiones avanzadas
- **Row Level Security**: Seguridad a nivel de fila nativa
- **GoTrue**: Autenticación JWT con múltiples proveedores
- **PostgREST**: API REST automática desde esquema de BD
- **Edge Functions**: Serverless Deno para lógica personalizada
- **Realtime**: WebSockets para actualizaciones en tiempo real
- **Storage**: Almacenamiento de archivos con CDN global

### **2.3 Inteligencia Artificial**
```typescript
// Configuración IA
const aiConfig = {
  openai: {
    model: 'gpt-4',
    embeddings: 'text-embedding-3-small',
    maxTokens: 4096
  },
  vectorSearch: {
    dimensions: 1536,
    similarity: 'cosine'
  }
}
```

**Stack de IA**:
- **OpenAI GPT-4**: Chatbot conversacional y procesamiento de lenguaje natural
- **OpenAI Embeddings**: Vectorización de texto para búsqueda semántica
- **pgvector**: Extensión PostgreSQL para búsqueda vectorial
- **Langchain.js**: Orquestación de workflows de IA

---

## **3. ARQUITECTURA DE COMPONENTES**

### **3.1 Diagrama de Arquitectura General**
```mermaid
graph TB
    subgraph "Frontend - Next.js 15"
        A[Portal Ciudadano] --> B[Chatbot IA]
        A --> C[Búsqueda Semántica]
        A --> D[Carpeta Ciudadana]
        A --> E[Seguimiento Trámites]
    end
    
    subgraph "Backend - Supabase"
        F[PostgreSQL + RLS] --> G[PostgREST API]
        F --> H[GoTrue Auth]
        F --> I[Edge Functions]
        F --> J[Storage]
    end
    
    subgraph "IA Services"
        K[OpenAI GPT-4] --> L[Chatbot Engine]
        M[OpenAI Embeddings] --> N[Vector Search]
        O[pgvector] --> N
    end
    
    subgraph "Admin Panel"
        P[Gestión Contenido] --> Q[CRUD Dependencias]
        P --> R[Gestión Usuarios]
        P --> S[Configuración IA]
    end
    
    A --> G
    B --> I
    C --> N
    I --> K
    I --> M
    G --> F
```

### **3.2 Arquitectura de Capas**
```
┌─────────────────────────────────────────┐
│           PRESENTACIÓN                  │
│  Next.js 15 App Router + React 18      │
│  Server Components + Client Components  │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             LÓGICA DE NEGOCIO           │
│  Server Actions + Edge Functions        │
│  Middleware + API Routes                │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            SERVICIOS                    │
│  Supabase Client + OpenAI SDK          │
│  Auth Providers + Storage               │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             DATOS                       │
│  PostgreSQL + RLS + pgvector            │
│  Realtime + Storage Buckets             │
└─────────────────────────────────────────┘
```

---

## **4. DISEÑO DE BASE DE DATOS**

### **4.1 Esquema Principal**
```sql
-- Extensiones requeridas
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Tabla de Dependencias
CREATE TABLE dependencias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    contacto JSONB,
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de Subdependencias
CREATE TABLE subdependencias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    id_dependencia UUID REFERENCES dependencias(id) ON DELETE CASCADE,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    contacto JSONB,
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de Categorías (Trámites, OPA, Servicios)
CREATE TABLE categorias (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    id_subdependencia UUID REFERENCES subdependencias(id) ON DELETE CASCADE,
    tipo categoria_tipo NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    palabras_clave TEXT[],
    contenido_embedding VECTOR(1536), -- Para búsqueda semántica
    fecha_publicacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ultima_actualizacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enum para tipos de categoría
CREATE TYPE categoria_tipo AS ENUM ('tramite', 'opa', 'servicio');

-- Tabla de Ciudadanos
CREATE TABLE ciudadanos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    nombre VARCHAR(255),
    apellido VARCHAR(255),
    identificacion_digital VARCHAR(50) UNIQUE,
    email VARCHAR(255),
    telefono VARCHAR(20),
    fecha_registro TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ultima_actividad TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **4.2 Políticas de Seguridad (RLS)**
```sql
-- Habilitar RLS en todas las tablas
ALTER TABLE dependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE subdependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE categorias ENABLE ROW LEVEL SECURITY;
ALTER TABLE ciudadanos ENABLE ROW LEVEL SECURITY;

-- Políticas para ciudadanos
CREATE POLICY "Ciudadanos pueden ver información pública"
ON categorias FOR SELECT
TO authenticated
USING (activo = true);

CREATE POLICY "Ciudadanos pueden acceder a su carpeta"
ON ciudadanos FOR ALL
TO authenticated
USING (auth.uid() = auth_id);

-- Políticas para administradores
CREATE POLICY "Admins pueden gestionar su dependencia"
ON dependencias FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM usuarios_administrativos ua
        WHERE ua.auth_id = auth.uid()
        AND ua.id_dependencia = dependencias.id
    )
);
```

---

## **5. FLUJOS DE DATOS Y APIs**

### **5.1 Edge Functions para IA**
```typescript
// supabase/functions/chat-ai/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'npm:@supabase/supabase-js@2'
import { OpenAI } from 'npm:openai@4'

serve(async (req: Request) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    {
      global: {
        headers: { Authorization: req.headers.get('Authorization')! },
      },
    }
  )

  const openai = new OpenAI({
    apiKey: Deno.env.get('OPENAI_API_KEY'),
  })

  const { pregunta, contexto } = await req.json()

  // Búsqueda semántica
  const embedding = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: pregunta,
  })

  const { data: documentos } = await supabase.rpc('buscar_contenido_similar', {
    query_embedding: embedding.data[0].embedding,
    match_threshold: 0.8,
    match_count: 5
  })

  // Generar respuesta con contexto
  const respuesta = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [
      {
        role: "system",
        content: `Eres un asistente de atención ciudadana. Usa el siguiente contexto para responder: ${JSON.stringify(documentos)}`
      },
      {
        role: "user",
        content: pregunta
      }
    ],
    max_tokens: 1000,
    temperature: 0.7
  })

  return new Response(
    JSON.stringify({ respuesta: respuesta.choices[0].message.content }),
    { headers: { "Content-Type": "application/json" } }
  )
})
```

### **5.2 Server Actions para Mutaciones**
```typescript
// app/actions/tramites.ts
'use server'

import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'

export async function crearTramite(formData: FormData) {
  const supabase = await createClient()
  
  const tramiteData = {
    nombre: formData.get('nombre') as string,
    descripcion: formData.get('descripcion') as string,
    tipo: 'tramite' as const,
    id_subdependencia: formData.get('subdependencia') as string
  }

  const { data, error } = await supabase
    .from('categorias')
    .insert(tramiteData)
    .select()

  if (error) throw error

  revalidatePath('/admin/tramites')
  return data
}
```

---

## **6. SEGURIDAD Y AUTENTICACIÓN**

### **6.1 Configuración de Autenticación**
```typescript
// utils/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // Server Component - ignorar errores de cookies
          }
        },
      },
    }
  )
}
```

### **6.2 Middleware de Autenticación**
```typescript
// middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value)
            response.cookies.set(name, value, options)
          })
        },
      },
    }
  )

  const { data: { user } } = await supabase.auth.getUser()

  // Proteger rutas de admin
  if (request.nextUrl.pathname.startsWith('/admin') && !user) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return response
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

---

## **7. ESTRUCTURA DEL PROYECTO**

### **7.1 Organización de Archivos Next.js 15**
```
chia-next/
├── app/                          # App Router (Next.js 15)
│   ├── (auth)/                   # Grupo de rutas de autenticación
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── register/
│   │       └── page.tsx
│   ├── (ciudadano)/              # Rutas para ciudadanos
│   │   ├── carpeta/
│   │   │   └── page.tsx
│   │   ├── tramites/
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx
│   │   │   └── page.tsx
│   │   └── chat/
│   │       └── page.tsx
│   ├── (admin)/                  # Panel administrativo
│   │   ├── admin/
│   │   │   ├── dependencias/
│   │   │   ├── usuarios/
│   │   │   └── dashboard/
│   │   └── layout.tsx
│   ├── api/                      # API Routes
│   │   ├── auth/
│   │   │   └── callback/
│   │   │       └── route.ts
│   │   └── webhooks/
│   │       └── route.ts
│   ├── globals.css
│   ├── layout.tsx                # Root layout
│   ├── loading.tsx               # Loading UI
│   ├── error.tsx                 # Error UI
│   └── page.tsx                  # Home page
├── components/                   # Componentes reutilizables
│   ├── ui/                       # Componentes base
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   └── dialog.tsx
│   ├── chat/                     # Componentes del chat
│   │   ├── ChatBot.tsx
│   │   ├── MessageList.tsx
│   │   └── MessageInput.tsx
│   ├── admin/                    # Componentes admin
│   │   ├── DataTable.tsx
│   │   └── FormBuilder.tsx
│   └── layout/                   # Componentes de layout
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── Footer.tsx
├── lib/                          # Utilidades y configuración
│   ├── supabase/
│   │   ├── client.ts
│   │   ├── server.ts
│   │   └── middleware.ts
│   ├── openai.ts
│   ├── utils.ts
│   └── validations.ts
├── hooks/                        # Custom hooks
│   ├── useAuth.ts
│   ├── useChat.ts
│   └── useLocalStorage.ts
├── types/                        # Definiciones de tipos
│   ├── database.ts
│   ├── auth.ts
│   └── api.ts
├── supabase/                     # Configuración Supabase
│   ├── functions/                # Edge Functions
│   │   ├── chat-ai/
│   │   └── search-semantic/
│   ├── migrations/               # Migraciones SQL
│   └── seed.sql                  # Datos iniciales
├── public/                       # Archivos estáticos
├── middleware.ts                 # Middleware Next.js
├── next.config.js               # Configuración Next.js
├── tailwind.config.js           # Configuración Tailwind
├── tsconfig.json                # Configuración TypeScript
└── package.json                 # Dependencias

---

## **8. IMPLEMENTACIÓN DE FUNCIONALIDADES CLAVE**

### **8.1 Chatbot IA Conversacional**
```typescript
// components/chat/ChatBot.tsx
'use client'

import { useState } from 'react'
import { useChat } from 'ai/react'

export default function ChatBot() {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    initialMessages: [
      {
        id: '1',
        role: 'assistant',
        content: '¡Hola! Soy tu asistente virtual. ¿En qué puedo ayudarte hoy?'
      }
    ]
  })

  return (
    <div className="flex flex-col h-96 border rounded-lg">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.role === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              {message.content}
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">
              Escribiendo...
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="p-4 border-t">
        <div className="flex space-x-2">
          <input
            value={input}
            onChange={handleInputChange}
            placeholder="Escribe tu pregunta..."
            className="flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            Enviar
          </button>
        </div>
      </form>
    </div>
  )
}
```

### **8.2 Búsqueda Semántica**
```typescript
// components/search/SemanticSearch.tsx
'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface SearchResult {
  id: string
  nombre: string
  descripcion: string
  tipo: string
  similarity: number
}

export default function SemanticSearch() {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const supabase = createClient()

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)
    try {
      const { data, error } = await supabase.functions.invoke('search-semantic', {
        body: { query: searchQuery }
      })

      if (error) throw error
      setResults(data.results || [])
    } catch (error) {
      console.error('Error en búsqueda:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      handleSearch(query)
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [query])

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Buscar trámites, servicios o información..."
          className="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
        />
        {isLoading && (
          <div className="absolute right-3 top-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>

      {results.length > 0 && (
        <div className="mt-4 bg-white border rounded-lg shadow-lg">
          {results.map((result) => (
            <div key={result.id} className="p-4 border-b last:border-b-0 hover:bg-gray-50">
              <h3 className="font-semibold text-lg text-blue-600">
                {result.nombre}
              </h3>
              <p className="text-gray-600 mt-1">{result.descripcion}</p>
              <div className="flex items-center mt-2 space-x-2">
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                  {result.tipo}
                </span>
                <span className="text-xs text-gray-500">
                  Relevancia: {Math.round(result.similarity * 100)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

### **8.3 Carpeta Ciudadana**
```typescript
// app/(ciudadano)/carpeta/page.tsx
import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export default async function CarpetaCiudadana() {
  const supabase = await createClient()

  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    redirect('/login')
  }

  const { data: ciudadano } = await supabase
    .from('ciudadanos')
    .select('*')
    .eq('auth_id', user.id)
    .single()

  const { data: tramites } = await supabase
    .from('seguimiento_tramites')
    .select(`
      *,
      categorias (
        nombre,
        tipo,
        descripcion
      )
    `)
    .eq('id_ciudadano', ciudadano?.id)
    .order('fecha_inicio', { ascending: false })

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Mi Carpeta Ciudadana</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Información Personal */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Información Personal</h2>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Nombre</label>
                <p className="text-lg">{ciudadano?.nombre} {ciudadano?.apellido}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p>{ciudadano?.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Identificación</label>
                <p>{ciudadano?.identificacion_digital}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Trámites en Curso */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Mis Trámites</h2>
            {tramites && tramites.length > 0 ? (
              <div className="space-y-4">
                {tramites.map((tramite) => (
                  <div key={tramite.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{tramite.categorias?.nombre}</h3>
                        <p className="text-gray-600 text-sm">{tramite.categorias?.descripcion}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        tramite.estado_actual === 'finalizado'
                          ? 'bg-green-100 text-green-800'
                          : tramite.estado_actual === 'en_proceso'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {tramite.estado_actual}
                      </span>
                    </div>
                    <div className="mt-3 text-sm text-gray-500">
                      Iniciado: {new Date(tramite.fecha_inicio).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No tienes trámites en curso.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
```

---

## **9. CONFIGURACIÓN DE DESPLIEGUE**

### **9.1 Configuración Vercel**
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
    "OPENAI_API_KEY": "@openai-api-key"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### **9.2 CI/CD Pipeline**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - run: npm ci
      - run: npm run build

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

---

## **10. CONSIDERACIONES DE RENDIMIENTO Y ESCALABILIDAD**

### **10.1 Optimizaciones Next.js**
- **Server Components**: Renderizado del lado del servidor para reducir JavaScript del cliente
- **Streaming**: Carga progresiva de componentes para mejor percepción de velocidad
- **Image Optimization**: Optimización automática de imágenes con formatos modernos
- **Font Optimization**: Carga optimizada de fuentes con `next/font`
- **Bundle Splitting**: División automática de código para cargas más rápidas

### **10.2 Optimizaciones Supabase**
```sql
-- Índices para búsqueda semántica
CREATE INDEX idx_categorias_embedding ON categorias
USING ivfflat (contenido_embedding vector_cosine_ops)
WITH (lists = 100);

-- Índices para consultas frecuentes
CREATE INDEX idx_categorias_activo ON categorias (activo);
CREATE INDEX idx_categorias_tipo ON categorias (tipo);
CREATE INDEX idx_seguimiento_ciudadano ON seguimiento_tramites (id_ciudadano);
CREATE INDEX idx_seguimiento_estado ON seguimiento_tramites (estado_actual);

-- Particionado para tablas grandes
CREATE TABLE consultas_ciudadanas_2024 PARTITION OF consultas_ciudadanas
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### **10.3 Estrategias de Caché**
```typescript
// lib/cache.ts
import { unstable_cache } from 'next/cache'

export const getCategoriasCached = unstable_cache(
  async (tipo: string) => {
    const supabase = createClient()
    const { data } = await supabase
      .from('categorias')
      .select('*')
      .eq('tipo', tipo)
      .eq('activo', true)

    return data
  },
  ['categorias'],
  {
    revalidate: 3600, // 1 hora
    tags: ['categorias']
  }
)
```

---

## **CONCLUSIONES Y PRÓXIMOS PASOS**

### **Fortalezas de la Arquitectura**
1. **Escalabilidad**: Arquitectura serverless que escala automáticamente
2. **Seguridad**: RLS nativo y autenticación robusta desde el diseño
3. **Performance**: SSR/SSG optimizado con caché inteligente
4. **Mantenibilidad**: Código TypeScript tipado y estructura modular
5. **Interoperabilidad**: APIs REST automáticas y Edge Functions personalizables

### **Próximos Pasos de Implementación**
1. **Fase 1 - Fundación** (Semanas 1-4):
   - Configuración inicial del proyecto Next.js + Supabase
   - Implementación del sistema de autenticación
   - Desarrollo del esquema de base de datos con RLS

2. **Fase 2 - IA Core** (Semanas 5-8):
   - Integración con OpenAI para chatbot
   - Implementación de búsqueda semántica con pgvector
   - Desarrollo de Edge Functions para IA

3. **Fase 3 - UX Ciudadano** (Semanas 9-12):
   - Portal ciudadano con componentes accesibles
   - Carpeta ciudadana digital
   - Sistema de seguimiento de trámites

4. **Fase 4 - Panel Admin** (Semanas 13-16):
   - Panel administrativo completo
   - Gestión de contenido y usuarios
   - Dashboards y analytics

Esta arquitectura proporciona una base sólida para una aplicación gubernamental moderna, escalable y centrada en la experiencia del ciudadano, cumpliendo con los más altos estándares de seguridad y accesibilidad.
```
